import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import electron from 'vite-plugin-electron'
import tailwindcss from '@tailwindcss/vite'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import { fileURLToPath } from 'url'

// https://vite.dev/config/
export default defineConfig(({mode}) => {
  const isDev = false //mode === 'development'
  console.log('Mode:', mode,isDev); // true
  return {
  base: './', // Sử dụng đường dẫn tương đối
  plugins: [
    vue(),
    tailwindcss(),
    isDev && electron({
      entry: {
        main: 'electron/index.js', // Bundle tất cả vào index.js
        preload: 'electron/preload.js' // preload riêng biệt
      },
      vite: {
        build: {
          outDir: 'electron-build',
          rollupOptions: {
            external: [
              // Exclude node modules from bundling
              'electron',
              'path',
              'fs',
              'child_process',
              'https',
              'http',
              'dotenv',
              'express',
              'cors',
              'morgan',
              'fluent-ffmpeg',
              'ffmpeg-static',
              'electron-store',
              'bytenode',
              'fs-extra',
              'uuid',
              'axios',
              'cheerio',
              'js-md5',
              'play-sound',
              'pinyin',
              'commander',
              'table',
              'tabulate',
              '@ffmpeg/ffmpeg',
              '@ffmpeg/core',
              'ffcreatorlite',
              // '@remotion/bundler',
              // '@remotion/renderer',
              // '@remotion/media-utils',
              'base64-js',
              'clsx',
              '@google/generative-ai',
              '@langchain/core',
              '@langchain/openai'
            ],
            output: {
              // Tên file rõ ràng
              entryFileNames: (chunk) => {
                if (chunk.name === 'main') return 'main.js'
                if (chunk.name === 'preload') return 'preload.js'
                return '[name].js'
              },
            },
          },
        },
      },
    }),
    AutoImport({
      imports: [
        'vue',
        { '@/globals/index': [['F', 'F'], ['S', 'S']] },
      ],
      dts: 'src/types/auto-imports.d.ts',
    }),
    Components({
      resolvers: [
        AntDesignVueResolver({importStyle: false}),
      ],
      // components
      dirs: ['src/components'],
      extensions: ['vue'],
      dts: 'src/types/components.d.ts',
      include: [/\.vue$/, /\.vue\?vue/, /\.vue\.[tj]sx?\?vue/],
    }),

  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  build: {
    assetsDir: 'assets', // Thư mục assets
    rollupOptions: {
      output: {
        assetFileNames: 'assets/[name]-[hash][extname]',
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js'
      }
    }
  },
  server: {
    port: 5173,
    strictPort: true,
    watch: {
      // Cho phép theo dõi thư mục cụ thể
      ignored: ['**/*', '!**/src/**']
      // ignored: ['**/*', '!**/electron/**','!**/src/**']
    }
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      }
    }
  }
}
})
