const md5 = require('js-md5');
const axios = require('axios');
const path = require('path');
const fs = require('fs');
const { audioTTSDir, getCurrentDir, getAudioDuration, convertToMilliseconds } = require('./utils');
const store = require('./store');
const TikTokTTS = require('./TikTokTTS');
const { soxStart } = require('./utils/sox');


function getSpeakers(type) {
      // if (type === 'tiktok') {
      //   const VOLCENGINE_VOICES = [
      //     { id: 'tts.other.BV075_streaming', gender: 'male', language: 'Vietnamese', name: '<PERSON><PERSON> tin <PERSON> (Vietnamese)' },
      //     { id: 'tts.other.BV074_streaming', gender: 'female', language: 'Vietnamese', name: '<PERSON><PERSON> gái hoạt ngôn <PERSON> (Vietnamese)' },
      //     { id: 'tts.other.BV562_streaming', gender: 'female', language: 'Vietnamese', name: '<PERSON><PERSON> (Vietnamese)' },
      //     { id: 'tts.other.BV560_streaming',gender: 'male', language: 'Vietnamese',  name: '<PERSON><PERSON> (Vietnamese)' },
      //     { id: 'tts.other.BV421_vivn_streaming', gender: 'female', language: 'Vietnamese', name: 'Nguồn nhỏ ngọt ngào Female (Vietnamese)' },
      //     { id: 'vi_female_huong', name: 'Giọng nữ phổ thông (Vietnamese)' },

      //     // { id: 'vi_female_huong', name: 'Giọng nữ phổ thông (Vietnamese)' },
      //     { id: 'zh_male_rap', gender: 'male', language: 'Chinese', name: 'Rap Male (Chinese)' },
      //     { id: 'zh_female_sichuan', gender: 'female', language: 'Chinese', name: 'Sichuan Female (Chinese)' },
      //     { id: 'tts.other.BV021_streaming', gender: 'male', language: 'Chinese', name: 'BV021 Male (Chinese)' },
      //     { id: 'tts.other.BV026_streaming', gender: 'male', language: 'Chinese', name: 'BV026 Male (Chinese)' },
      //     { id: 'tts.other.BV025_streaming', gender: 'female', language: 'Chinese', name: 'BV025 Female (Chinese)' },
      //     { id: 'zh_male_xiaoming', gender: 'male', language: 'Chinese', name: 'Xiaoming Male (Chinese)' },
      //     { id: 'zh_male_zhubo', gender: 'male', language: 'Chinese', name: 'Zhubo Male (Chinese)' },
      //     { id: 'zh_female_zhubo', gender: 'female', language: 'Chinese', name: 'Zhubo Female (Chinese)' },
      //     { id: 'zh_female_qingxin', gender: 'female', language: 'Chinese', name: 'Qingxin Female (Chinese)' },
      //     { id: 'zh_female_story', gender: 'female', language: 'Chinese', name: 'Story Female (Chinese)' },
      //     { id: 'en_male_adam', gender: 'male', language: 'English', name: 'Adam Male (English)' },
      //     { id: 'tts.other.BV027_streaming', gender: 'female', language: 'English', name: 'BV027 Female (English)' },
      //     { id: 'en_male_bob', gender: 'male', language: 'English', name: 'Bob Male (English)' },
      //     { id: 'tts.other.BV032_TOBI_streaming', gender: 'female', language: 'English', name: 'TOBI Female (English)' },
      //     { id: 'tts.other.BV516_streaming', gender: 'male', language: 'English', name: 'BV516 Male (English)' },
      //     { id: 'en_female_sarah', gender: 'female', language: 'English', name: 'Sarah Female (English)' },
      //     { id: 'jp_male_satoshi', gender: 'male', language: 'Japanese', name: 'Satoshi Male (Japanese)' },
      //     { id: 'jp_female_mai', gender: 'female', language: 'Japanese', name: 'Mai Female (Japanese)' },
      //     { id: 'kr_male_gye', gender: 'male', language: 'Korean',  name: 'Gye Male (Korean)' },
      //     { id: 'tts.other.BV059_streaming', gender: 'female', language: 'Korean', name: 'Jin Female (Korean)' },
      //     { id: 'fr_male_enzo', gender: 'male', language: 'French', name: 'Enzo Male (French)' },
      //     { id: 'tts.other.BV078_streaming', gender: 'female', language: 'French', name: 'Jade Female (French)' },
      //     { id: 'de_female_sophie', gender: 'female', language: 'German', name: 'Sophie Female (German)' },
      //     { id: 'es_male_george', gender: 'female', language: 'Spanish', name: 'George Male (Spanish)' },
      //     { id: 'tts.other.BV065_streaming', gender: 'female', language: 'Spanish', name: 'Carmen Female (Spanish)' },
      //     { id: 'tts.other.BV068_streaming', gender: 'female', language: 'Russian', name: 'Russian Female (Russian)' },
      //     { id: 'tts.other.BV087_streaming', gender: 'male', language: 'Italian', name: 'Italian Male (Italian)' },
      //     { id: 'tts.other.BV083_streaming', gender: 'male', language: 'Turkish', name: 'Turkish Male (Turkish)' },
      //     { id: 'tts.other.BV531_streaming', gender: 'male', language: 'Portugese', name: 'Portugese Male (Portugese)' },
      //     { id: 'pt_female_alice', gender: 'female', language: 'Portugese', name: 'Alice Female (Portugese)' },
      //     { id: 'tts.other.BV092_streaming', gender: 'female', language: 'Malay', name: 'Malay Female (Malay)' },
      //     { id: 'tts.other.BV570_streaming', gender: 'male', language: 'Arabic', name: 'Arabic Male (Arabic)' },
      //     { id: 'tts.other.BV160_streaming', gender: 'male', language: 'Indonesian', name: 'Indonesian Male (Indonesian)' },
      //     { id: 'id_female_noor', gender: 'female', language: 'Indonesian', name: 'Noor Female (Indonesian)' },
      //   ];
      //   return VOLCENGINE_VOICES
      // }

      const speakers = [
        { id: 'BV421_vivn_streaming', name: 'Nguồn nhỏ ngọt ngào (Vietnamese)' },
        { id: 'BV075_streaming', name: 'Tự tin (Vietnamese)' },
        { id: 'BV074_streaming', name: 'Cô gái hoạt ngôn (Vietnamese)' },
        { id: 'BV562_streaming', name: 'Chí mai (Vietnamese)' },
        { id: 'BV560_streaming', name: 'Anh Dũng (Vietnamese)' },
        { id: 'vi_female_huong', name: 'Giọng nữ phổ thông (Vietnamese)' },
        { id: 'BV525_streaming', name: '謎1 男子1 (Japanese)' },
        { id: 'BV528_streaming', name: '謎2 坊や (Japanese)' },
        { id: 'BV017_streaming', name: 'カワボ (Japanese)' },
        { id: 'BV016_streaming', name: 'お姉さん (Japanese)' },
        { id: 'BV023_streaming', name: '少女 (Japanese)' },
        { id: 'BV024_streaming', name: '女子 (Japanese)' },
        { id: 'BV018_streaming', name: '男子2 (Japanese)' },
        { id: 'BV523_streaming', name: '坊ちゃん (Japanese)' },
        { id: 'BV521_streaming', name: '女子 (Japanese)' },
        { id: 'BV522_streaming', name: '女子アナ (Japanese)' },
        { id: 'BV524_streaming', name: '男性アナ (Japanese)' },
        { id: 'BV520_streaming', name: '元気ロリ (Japanese)' },
        { id: 'VOV401_bytesing3_kangkangwuqu', name: '明るいハニー (Japanese)' },
        { id: 'VOV402_bytesing3_oh', name: '優しいレディー (Japanese)' },
        { id: 'VOV402_bytesing3_aidelizan', name: '風雅メゾソプラノ (Japanese)' },
        { id: 'jp_005', name: 'Sakura (Japanese)' },
        
        // Bổ sung giọng Japanese từ VOLCENGINE_VOICES
        { id: 'jp_male_satoshi', name: 'Satoshi Male (Japanese)' },
        { id: 'jp_female_mai', name: 'Mai Female (Japanese)' },

        // ↓ Dưới đây là các voice tiếng Anh
        { id: 'BV511_streaming', name: 'Ava - Nữ lười biếng (English)' },
        { id: 'BV505_streaming', name: 'Alicia - Nữ nghị luận (English)' },
        { id: 'BV138_streaming', name: 'Lawrence - Nữ cảm xúc (English)' },
        { id: 'BV027_streaming', name: 'Amelia - Nữ Mỹ (English)' },
        { id: 'BV502_streaming', name: 'Amanda - Nữ kể chuyện (English)' },
        { id: 'BV503_streaming', name: 'Ariana - Nữ năng động (English)' },
        { id: 'BV504_streaming', name: 'Jackson - Nam năng động (English)' },
        { id: 'BV421_streaming', name: 'Thiên tài thiếu nữ (English)' },
        { id: 'BV702_streaming', name: 'Stefan (English)' },
        { id: 'BV506_streaming', name: 'Lily - Em bé ngây thơ (English)' },
        { id: 'BV040_streaming', name: 'Anna - Nữ thân thiện (English)' },
        { id: 'BV516_streaming', name: 'Henry - Nam Úc (English)' },
        
        // Bổ sung giọng English từ VOLCENGINE_VOICES
        { id: 'en_male_adam', name: 'Adam Male (English)' },
        { id: 'en_male_bob', name: 'Bob Male (English)' },
        { id: 'BV032_TOBI_streaming', name: 'TOBI Female (English)' },
        { id: 'en_female_sarah', name: 'Sarah Female (English)' },

        // ↓ Dưới đây là các voice tiếng Trung đã convert
        { id: 'BV700_V2_streaming', name: 'Cận Cận 2.0 (Chinese)' },
        { id: 'BV705_streaming', name: 'Dương Dương (Chinese)' },
        { id: 'BV701_V2_streaming', name: 'Kình Thương 2.0 (Chinese)' },
        { id: 'BV001_V2_streaming', name: 'Giọng nữ phổ thông 2.0 (Chinese)' },
        { id: 'BV700_streaming', name: 'Cận Cận (Chinese)' },
        { id: 'BV406_V2_streaming', name: 'Siêu nhiên - Tử Tử 2.0 (Chinese)' },
        { id: 'BV406_streaming', name: 'Siêu nhiên - Tử Tử (Chinese)' },
        {
          id: 'BV407_V2_streaming',
          name: 'Siêu nhiên - Nhiên Nhiên 2.0 (Chinese)',
        },
        { id: 'BV407_streaming', name: 'Siêu nhiên - Nhiên Nhiên (Chinese)' },
        { id: 'BV001_streaming', name: 'Giọng nữ phổ thông (Chinese)' },
        { id: 'BV002_streaming', name: 'Giọng nam phổ thông (Chinese)' },
        { id: 'BV701_streaming', name: 'Kình Thương (Chinese)' },
        { id: 'BV123_streaming', name: 'Thanh niên tỏa sáng (Chinese)' },
        { id: 'BV119_streaming', name: 'Con rể phổ thông (Chinese)' },
        { id: 'BV115_streaming', name: 'Thiếu gia cổ phong (Chinese)' },
        { id: 'BV107_streaming', name: 'Chú trung niên bá đạo (Chinese)' },
        { id: 'BV100_streaming', name: 'Thanh niên chất phác (Chinese)' },
        { id: 'BV104_streaming', name: 'Thiếu nữ dịu dàng (Chinese)' },
        { id: 'BV004_streaming', name: 'Thanh niên cởi mở (Chinese)' },
        { id: 'BV113_streaming', name: 'Thiếu gia ngọt ngào (Chinese)' },
        { id: 'BV102_streaming', name: 'Thanh niên nhã nhặn (Chinese)' },
        { id: 'BV405_streaming', name: 'Tiểu Nguyên ngọt ngào (Chinese)' },
        { id: 'BV007_streaming', name: 'Giọng nữ thân thiện (Chinese)' },
        { id: 'BV009_streaming', name: 'Giọng nữ tri thức (Chinese)' },
        { id: 'BV419_streaming', name: 'Thành Thành (Chinese)' },
        { id: 'BV415_streaming', name: 'Đồng Đồng (Chinese)' },
        { id: 'BV008_streaming', name: 'Giọng nam thân thiện (Chinese)' },
        { id: 'BV408_streaming', name: 'Giọng nam lồng tiếng (Chinese)' },
        { id: 'BV426_streaming', name: 'Cừu lười nhỏ (Chinese)' },
        { id: 'BV428_streaming', name: 'Giọng nữ văn nghệ tươi mới (Chinese)' },
        { id: 'BV403_streaming', name: "Giọng nữ 'súp gà' (Chinese)" },
        { id: 'BV158_streaming', name: 'Lão giả thông thái (Chinese)' },
        { id: 'BV157_streaming', name: 'Bà ngoại nhân hậu (Chinese)' },
        { id: 'BR001_streaming', name: 'Anh chàng rap (Chinese)' },
        {
          id: 'BV410_streaming',
          name: 'Bình luận viên nam năng động (Chinese)',
        },
        { id: 'BV411_streaming', name: 'Bình luận viên đẹp trai (Chinese)' },
        { id: 'BV437_streaming', name: 'Bình luận viên cảm xúc (Chinese)' },
        { id: 'BV412_streaming', name: 'Bình luận viên xinh đẹp (Chinese)' },
        { id: 'BV159_streaming', name: 'Thanh niên ăn chơi (Chinese)' },
        { id: 'BV418_streaming', name: 'Nữ streamer số một (Chinese)' },
        { id: 'BV120_streaming', name: "Thanh niên chống 'cuộn' (Chinese)" },
        {
          id: 'BV142_streaming',
          name: 'Bình luận viên nam điềm đạm (Chinese)',
        },
        { id: 'BV143_streaming', name: 'Thanh niên phóng khoáng (Chinese)' },
        { id: 'BV056_streaming', name: 'Giọng nam tỏa sáng (Chinese)' },
        { id: 'BV005_streaming', name: 'Giọng nữ hoạt bát (Chinese)' },
        { id: 'BV064_streaming', name: 'Bé gái nhỏ dễ thương (Chinese)' },
        { id: 'BV051_streaming', name: 'Em bé dễ thương (Chinese)' },
        { id: 'BV063_streaming', name: 'SpongeBob (hoạt hình) (Chinese)' },
        { id: 'BV417_streaming', name: 'Patrick (hoạt hình) (Chinese)' },
        { id: 'BV050_streaming', name: 'Shin (hoạt hình) (Chinese)' },
        { id: 'BV061_streaming', name: 'Giọng thiếu nhi thiên tài (Chinese)' },
        { id: 'BV401_streaming', name: 'Giọng nam khuyến mãi (Chinese)' },
        { id: 'BV402_streaming', name: 'Giọng nữ khuyến mãi (Chinese)' },
        { id: 'BV006_streaming', name: 'Giọng nam trầm ấm (Chinese)' },
        { id: 'BV011_streaming', name: 'Giọng nữ thời sự (Chinese)' },
        { id: 'BV012_streaming', name: 'Giọng nam thời sự (Chinese)' },
        { id: 'BV034_streaming', name: 'Chị gái tri thức (Chinese)' },
        { id: 'BV033_streaming', name: 'Anh trai dịu dàng (Chinese)' },

        // Theo vùng miền Trung Quốc
        { id: 'BV021_streaming', name: 'Anh Đông Bắc (Chinese)' },
        { id: 'BV020_streaming', name: 'Cô gái Đông Bắc (Chinese)' },
        { id: 'BV704_streaming', name: 'Cận Cận (Đông Bắc) (Chinese)' },
        { id: 'BV210_streaming', name: 'Cô chủ Tây An (Chinese)' },
        { id: 'BV217_streaming', name: 'Chị gái Thượng Hải (Chinese)' },
        { id: 'BV213_streaming', name: 'Anh họ Quảng Tây (Chinese)' },
        { id: 'BV704_streaming', name: 'Cận Cận (Quảng Tây) (Chinese)' },
        { id: 'BV025_streaming', name: 'Cô gái Đài Loan dễ thương (Chinese)' },
        {
          id: 'BV227_streaming',
          name: 'Giọng nam phổ thông Đài Loan (Chinese)',
        },
        { id: 'BV704_streaming', name: 'Cận Cận (Đài Loan) (Chinese)' },
        { id: 'BV026_streaming', name: 'Nam thần TVB (Chinese)' },
        { id: 'BV424_streaming', name: 'Cô gái Quảng Đông (Chinese)' },
        { id: 'BV704_streaming', name: 'Cận Cận (Quảng Đông) (Chinese)' },
        { id: 'BV212_streaming', name: 'Diễn viên tấu nói (Chinese)' },
        { id: 'BV019_streaming', name: 'Anh chàng Trùng Khánh (Chinese)' },
        { id: 'BV221_streaming', name: 'Cô em ngọt ngào Tứ Xuyên (Chinese)' },
        { id: 'BV423_streaming', name: 'Em gái Trùng Khánh (Chinese)' },
        { id: 'BV704_streaming', name: 'Cận Cận (Thành Đô) (Chinese)' },
        {
          id: 'BV214_streaming',
          name: 'Doanh nhân nông thôn Trịnh Châu (Chinese)',
        },
        { id: 'BV226_streaming', name: 'Cô gái Hồ Nam (Chinese)' },
        { id: 'BV216_streaming', name: 'Mỹ nữ Trường Sa (Chinese)' },
        
        // Bổ sung giọng Chinese từ VOLCENGINE_VOICES
        { id: 'zh_male_rap', name: 'Rap Male (Chinese)' },
        { id: 'zh_female_sichuan', name: 'Sichuan Female (Chinese)' },
        { id: 'zh_male_xiaoming', name: 'Xiaoming Male (Chinese)' },
        { id: 'zh_male_zhubo', name: 'Zhubo Male (Chinese)' },
        { id: 'zh_female_zhubo', name: 'Zhubo Female (Chinese)' },
        { id: 'zh_female_qingxin', name: 'Qingxin Female (Chinese)' },
        { id: 'zh_female_story', name: 'Story Female (Chinese)' },
        
        // Bổ sung các ngôn ngữ khác từ VOLCENGINE_VOICES
        { id: 'kr_male_gye', name: 'Gye Male (Korean)' },
        { id: 'BV059_streaming', name: 'Jin Female (Korean)' },
        { id: 'fr_male_enzo', name: 'Enzo Male (French)' },
        { id: 'BV078_streaming', name: 'Jade Female (French)' },
        { id: 'de_female_sophie', name: 'Sophie Female (German)' },
        { id: 'es_male_george', name: 'George Male (Spanish)' },
        { id: 'BV065_streaming', name: 'Carmen Female (Spanish)' },
        { id: 'BV068_streaming', name: 'Russian Female (Russian)' },
        { id: 'BV087_streaming', name: 'Italian Male (Italian)' },
        { id: 'BV083_streaming', name: 'Turkish Male (Turkish)' },
        { id: 'BV531_streaming', name: 'Portugese Male (Portugese)' },
        { id: 'pt_female_alice', name: 'Alice Female (Portugese)' },
        { id: 'BV092_streaming', name: 'Malay Female (Malay)' },
        { id: 'BV570_streaming', name: 'Arabic Male (Arabic)' },
        { id: 'BV160_streaming', name: 'Indonesian Male (Indonesian)' },
        { id: 'id_female_noor', name: 'Noor Female (Indonesian)' },
      ];

      return speakers

}


const CAPCUT_CONFIG = {
  baseUrl: 'https://edit-api-sg.capcut.com',
  headers: {
    appid: '348188',
    pf: '7',
    appvr: '12.4.0',
    'sign-ver': '1',
    'app-sdk-version': '48.0.0',
    'content-type': 'application/json',
    Referer: 'https://www.capcut.com/',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
  },
};

function getHeaders(cookie) {
    const session = store.get('session') || {};
  return {
    ...CAPCUT_CONFIG.headers,
    Cookie: cookie || session.cookie,
  };
}

function getParsedURL(path, baseOrigin = 'http://localhost') {
  let absoluteURL;
  try {
    absoluteURL = new URL(path);
  } catch (err) {
    absoluteURL = new URL(path, baseOrigin);
  }
  return absoluteURL;
}

function setupAxiosInterceptors(options = {}) {
  const axiosInstance = axios.create(options);
  axiosInstance.interceptors.request.use((config) => {
    // Destructure config
    let { url = '', baseURL = '', headers, data = {} } = config;

    // Lấy path cuối của URL (7 ký tự)
    const { pathname } = getParsedURL(`${baseURL}${url}`);
    const shortPath = pathname.length >= 7 ? pathname.slice(-7) : pathname;

    // Thiết lập headers mặc định
    headers.lan = 'zh-hans';
    headers.pf = '1';
    headers.appvr = options.appVersion || CAPCUT_CONFIG.headers.appvr;
    headers.tdid = 'web';
    headers['sign-ver'] = 1;
    headers['device-time'] = Math.floor(Date.now() / 1000);
    headers['user-agent'] =
      process.env.USER_AGENT ||
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36';

    // Tạo chuỗi ký và sinh hash
    const signString = `9e2c|${shortPath}|${headers.pf}|${headers.appvr}|${headers['device-time']}|${headers.tdid}|11ac`;
    headers.sign = md5(signString).toLowerCase();

    // Gán lại data và headers
    config.data = data;
    config.headers = headers;

    return config;
  });
  axiosInstance.interceptors.response.use(
    (response) => {
      return response;
    },
    (error) => {
      if (error.response) {
        // Xử lý lỗi từ phía server
        console.error('Server Error:', error.response.data);
      } else if (error.request) {
        // Xử lý lỗi khi không có phản hồi từ server
        console.error('No response from server:', error.request);
      } else {
        // Xử lý lỗi khác
        console.error('Error:', error.message);
      }
      return Promise.reject(error);
    },
  );
  return axiosInstance;
}


// create slug from text
function slugify(text) {
  return text
    .normalize('NFD')                     // Tách dấu khỏi ký tự
    .replace(/[\u0300-\u036f]/g, '')      // Xóa các dấu
    .replace(/đ/g, 'd')                   // Chuyển đ -> d
    .replace(/Đ/g, 'd')
    .toLowerCase()
    .replace(/\s+/g, '-')                 // Thay khoảng trắng bằng -
    .replace(/[^\w\-]+/g, '')             // Xóa ký tự không phải chữ/số/gạch ngang
    .replace(/\-\-+/g, '-')               // Gộp nhiều dấu - liên tiếp
    .replace(/^-+|-+$/g, '');             // Xóa - ở đầu và cuối
}
function detectLang(text) {
  // Regex nhận diện tiếng Trung (ký tự CJK Unified Ideographs)
  const chineseRegex = /[\u4E00-\u9FFF]/;

  // Regex nhận diện dấu tiếng Việt
  const vietnameseRegex = /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i;

  if (chineseRegex.test(text)) {
    return 'zh';
  } else if (vietnameseRegex.test(text)) {
    return 'vi';
  } else {
    return 'en';
  }
}

async function synthesizeService(event,{ text, speaker, typeEngine, audio_config,language, openaiConfig, mimimaxConfig }) {
      console.log('audio_config: ', { text, speaker, typeEngine, audio_config,language, openaiConfig, mimimaxConfig });
      const session = store.get('session') || {};
      const audioFilePath = path.join(getCurrentDir(), slugify(text).slice(0, 24) + Date.now() + '.wav');
      const type = 'synthesize-service-res';

      // Mimimax TTS
      if (typeEngine === 'mimimax') {
        if (event) {
          event.sender.send(type, { data: 'Generating audio with Mimimax TTS...', code: 0 });
        }

        try {
          const mimimaxTTS = require('./mimimaxTTS');

          const result = await mimimaxTTS.generateTTS({
            text: text,
            apiKey: mimimaxConfig.apiKey,
            groupId: mimimaxConfig.groupId,
            voiceId: speaker,
            voiceSettings: {
              voice_id: speaker,
              speed: audio_config.speech_rate || 1,
              vol: mimimaxConfig.voiceSettings?.vol || 1,
              pitch: audio_config.pitch_rate || 0
            },
            audioSettings: {
              sample_rate: mimimaxConfig.audioSettings?.sample_rate || 32000,
              bitrate: mimimaxConfig.audioSettings?.bitrate || 128000,
              format: mimimaxConfig.audioSettings?.format || 'mp3',
              channel: mimimaxConfig.audioSettings?.channel || 1
            }
          });

          if (result.success) {
            const duration = await getAudioDuration(result.audioFilePath);

            if (event) {
              event.sender.send(type, {
                data: 'Mimimax TTS generation completed successfully',
                code: 1,
                audioUrl: result.audioUrl,
                duration: convertToMilliseconds(duration)
              });
            }

            return {
              success: true,
              duration: convertToMilliseconds(duration),
              audioUrl: result.audioUrl,
              audioLink: result.audioFilePath
            };
          } else {
            throw new Error('Mimimax TTS generation failed');
          }
        } catch (error) {
          console.error('Mimimax TTS error:', error);
          if (event) {
            event.sender.send(type, {
              data: `Mimimax TTS error: ${error.message}`,
              code: -1
            });
          }
          throw error;
        }
      }

      // OpenAI TTS
      if (typeEngine === 'openai') {
        if (event) {
          event.sender.send(type, { data: 'Generating audio with OpenAI TTS...', code: 0 });
        }

        try {
          const response = await axios.post(
            `https://api.openai.com/v1/audio/speech`,
            {
              model: 'tts-1',
              input: text,
              voice: speaker,
              speed: openaiConfig.speed || 1.0,
              response_format: openaiConfig.format || 'wav',
            },
            {
              headers: {
                'Authorization': `Bearer ${openaiConfig.apiKey}`,
                'Content-Type': 'application/json',
              },
              responseType: 'arraybuffer'
            }
          );

          fs.writeFileSync(audioFilePath, Buffer.from(response.data));
          console.log(`✅ OpenAI TTS saved to ${audioFilePath}`);

          if (event) {
            event.sender.send(type, { data: `Audio generated ${audioFilePath}`, code: 0 });
          }
          const reverb = audio_config.reverb;
          const pitch = audio_config.pitch_rate?.toString()
          if (reverb || pitch) {
            console.log('Applying audio effects...');
            const tempFilePath = path.join(getCurrentDir(), slugify(text).slice(0, 24) + Date.now() + '_temp.wav');
            await soxStart(audioFilePath, tempFilePath, {
              reverb,
              pitch
            });
            fs.unlinkSync(audioFilePath); // Xóa file gốc
            fs.renameSync(tempFilePath, audioFilePath); // Đổi tên file tạm thành file gốc
          }
          return {
            success: true,
            duration: convertToMilliseconds(await getAudioDuration(audioFilePath)),
            audioUrl: `file://${audioFilePath}`,
          };
        } catch (error) {
          console.error('❌ OpenAI TTS Error:', error.message);
          if (event) {
            event.sender.send(type, { data: `OpenAI TTS Error: ${error.message}`, code: 1 });
          }
          return {
            success: false,
            message: error.message,
          };
        }
      }

      if (typeEngine === 'tiktok') {
        if (event) {
          event.sender.send(type, { data: 'Generating audio...', code: 0 });
        }
        // TikTokTTS
        const tiktokTTS = new TikTokTTS({ tiktok_sessionid: session.tiktokSessionId });
        const audioUrl = await tiktokTTS.run(text, audioFilePath, speaker, true);
        if (event) {
          event.sender.send(type, { data: `Audio generated ${audioUrl}`, code: 0 });
        }

          const reverb = audio_config.reverb;
          const pitch = audio_config.pitch_rate?.toString()
          const speed = audio_config.speech_rate || 1;
          if (reverb || pitch || speed !== 1) {
            console.log('Applying audio effects...', pitch);
            const tempFilePath = path.join(getCurrentDir(), slugify(text).slice(0, 24) + Date.now() + '_temp.wav');
            await soxStart(audioFilePath, tempFilePath, {
              reverb,
              pitch,
              speed
            });
            fs.unlinkSync(audioFilePath); // Xóa file gốc
            fs.renameSync(tempFilePath, audioFilePath); // Đổi tên file tạm thành file gốc
          }
        return {
          success: true,
          duration: convertToMilliseconds(await getAudioDuration(audioUrl)),
          audioUrl: `file://${audioUrl}`,
        };
        // return await synthesizeAndSave(text, speaker,language);
      }
      function convertSpeedToPercent(speed) {
        const res= 24 * speed * speed + 27 * speed - 50;
        return Math.round(res);
      }
      const axiosInstance = setupAxiosInterceptors({
        baseURL: CAPCUT_CONFIG.baseUrl,
        headers: CAPCUT_CONFIG.headers,
        appVersion: CAPCUT_CONFIG.headers.appvr,
      });
      // Create TTS
      const reverb = audio_config.reverb;
      delete audio_config['reverb'];
      if(audio_config.speech_rate == 1) delete audio_config['speech_rate'];
      if (audio_config.speech_rate) {
        audio_config.speech_rate = convertSpeedToPercent(audio_config.speech_rate);
      }
      const createBody = {
        workspace_id: session.workspaceId,
        smart_tool_type: 39,
        scene: 3,
        params: JSON.stringify({
          text: text,
          platform: 1,
        }),
        req_json: JSON.stringify({
          speaker: speaker,
          audio_config,
          disable_caption: true,
        }),
      };
      console.log('createBody: ', createBody);
      const createResponse = await axiosInstance.post(
        `${CAPCUT_CONFIG.baseUrl}/lv/v2/intelligence/create?aid=348188&device_platform=web&region=SG&web_id=7493795276948899345`,
        createBody,
        { headers: getHeaders() },
      );
      if (event) {
        event.sender.send(type, { data: 'Generating audio...', code: 0 });
      }
      // console.log('createResponse: ', createResponse.data);

      if (!createResponse.data.data || !createResponse.data.data.task_id) {
        if (event) {
          event.sender.send(type, { data: 'Failed to create TTS task', code: 1 });
        }
        console.log('createResponse: ', createResponse.data);
        throw new Error('Failed to create TTS task');
      }

      const taskId = createResponse.data.data.task_id;

      // Query TTS status
      let retries = 0;
      const maxRetries = 30;
      const retryInterval = 1000;
      let audioUrl = null;
      let duration = 0;

      while (retries < maxRetries) {
        await new Promise((resolve) => setTimeout(resolve, retryInterval));

        const queryBody = {
          task_id: taskId,
          workspace_id: session.workspaceId,
          smart_tool_type: 39,
        };

        const queryResponse = await axiosInstance.post(
          `${CAPCUT_CONFIG.baseUrl}/lv/v2/intelligence/query?aid=348188&device_platform=web&region=SG&web_id=7493795276948899345`,
          queryBody,
          { headers: getHeaders() },
        );

        if (queryResponse.data.data && queryResponse.data.data.status === 2) {
          if (
            queryResponse.data.data.task_detail &&
            queryResponse.data.data.task_detail[0] &&
            queryResponse.data.data.task_detail[0].url
          ) {
            audioUrl = queryResponse.data.data.task_detail[0].url;
            duration = queryResponse.data.data.task_detail[0].duration;
            break;
          }
        }

        retries++;
      }

      if (!audioUrl) {
        if (event) {
          event.sender.send(type, { data: 'Failed to get audio URL after multiple attempts', code: 1 });
        }
        throw new Error('Failed to get audio URL after multiple attempts');
      }
      // download audio and save to local
      const audioBuffer = await axiosInstance.get(audioUrl, { responseType: 'arraybuffer' });
      
      fs.writeFileSync(audioFilePath, Buffer.from(audioBuffer.data, 'binary'));
      console.log(`✅ Lưu thành công vào ${audioFilePath}`);
      if (event) {
        event.sender.send(type, { data: `Audio generated ${audioFilePath}`, code: 0 });
      }
      // run sox if reverb is true
      if (reverb) {
        console.log('Applying reverb effect...');
        const tempFilePath = path.join(getCurrentDir(), slugify(text).slice(0, 24) + Date.now() + '_temp.wav');
        await soxStart(audioFilePath, tempFilePath, {
          reverb,
        });
        fs.unlinkSync(audioFilePath); // Xóa file gốc
        fs.renameSync(tempFilePath, audioFilePath); // Đổi tên file tạm thành file gốc
      }
      return {
        success: true,
        duration,
        audioUrl: `file://${audioFilePath}`,
        audioLink:audioUrl
      };
}

module.exports = {
  getSpeakers,
  synthesizeService,
};
