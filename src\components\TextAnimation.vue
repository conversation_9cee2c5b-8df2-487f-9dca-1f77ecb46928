<template>
  <a-form-item label="Text Animation/watermark">
    <a-checkbox v-model:checked="renderOptions.showText">
      Show text Animation
    </a-checkbox>
    <div v-if="renderOptions.showText" class="mt-2">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-item label="Text">
            <a-input v-model:value="renderOptions.textValue" type="text" placeholder="Nhập text..." />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="Saved Texts">
            <div class="flex gap-2">
              <a-select v-model:value="selectedSavedText" placeholder="Chọn text đã lưu" style="flex: 1"
                @change="onSelectSavedText" allow-clear>
                <a-select-option v-for="(text, index) in savedTexts" :key="index" :value="index">
                  <div class="flex justify-between items-center">
                    <span>{{ text.length > 20 ? text.substring(0, 20) + '...' : text }}</span>
                    <a-button type="text" size="small" danger @click.stop="removeSavedText(index)" class="ml-2">
                      ×
                    </a-button>
                  </div>
                </a-select-option>
              </a-select>
              <a-button type="primary" size="small" @click="saveCurrentText"
                :disabled="!renderOptions.textValue || renderOptions.textValue.trim() === ''" title="Lưu text hiện tại">
                💾
              </a-button>
            </div>
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-form-item label="Font Size">
            <a-input-number v-model:value="renderOptions.fontSize" :min="12" :max="72" addon-after="px" />
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-form-item label="Text Color">
            <a-input v-model:value="renderOptions.textColor" type="color" />
          </a-form-item>
        </a-col>

      </a-row>
      <!-- Font Selection -->
      <a-row :gutter="16" class="mb-4">
        <!-- direction -->
        <a-col :span="6">
          <a-form-item label="Hướng di chuyển">
            <a-select v-model:value="renderOptions.textDirection">
              <a-select-option value="random">Random</a-select-option>
              <a-select-option value="updown">Up & Down</a-select-option>
              <a-select-option value="leftright">Left & Right</a-select-option>
              <a-select-option value="diagonal">Diagonal</a-select-option>
              <a-select-option value="all">All</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <!-- text speed -->
        <a-col :span="4">
          <a-form-item label="Text Speed">
            <a-slider v-model:value="renderOptions.textSpeed" :min="30" :max="120" />
          </a-form-item>
        </a-col>
        <!-- opacity -->
        <a-col :span="4">
          <a-form-item label="Opacity">
            <a-slider v-model:value="renderOptions.textOpacity" :min="0" :max="100" />
          </a-form-item>
        </a-col>
        <a-col :span="9">
          <a-form-item label="Font Family">
            <a-select v-model:value="renderOptions.textFontFamily" placeholder="Select font"
              :loading="!fontService.isReady()">
              <a-select-option v-for="font in fontService.getFonts()" :key="font.name" :value="font.name">
                <span
                  :style="{ fontFamily: font.isSystem ? font.name : (loadedFonts.has(font.path) ? font.name : 'inherit') }">
                  {{ font.name }}
                  <span v-if="!font.isSystem" class="text-gray-400 text-xs ml-2">(Custom)</span>
                </span>
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </div>
  </a-form-item>
</template>
<script setup>
import { ref, onMounted, watch } from 'vue';
import { useSubtitleStore } from '@/stores/subtitle-store';
import fontService from '@/services/fontService';

const subtitleStore = useSubtitleStore();
const renderOptions = subtitleStore.renderOptions;

// Font loading state
const loadedFonts = ref(new Set());

// Saved texts functionality
const savedTexts = ref([]);
const selectedSavedText = ref(null);

// Load saved texts from localStorage
const loadSavedTexts = () => {
  try {
    const saved = localStorage.getItem('tts-app-saved-texts');
    if (saved) {
      savedTexts.value = JSON.parse(saved);
    }
  } catch (error) {
    console.error('Error loading saved texts:', error);
    savedTexts.value = [];
  }
};

// Save texts to localStorage
const saveSavedTexts = () => {
  try {
    localStorage.setItem('tts-app-saved-texts', JSON.stringify(savedTexts.value));
  } catch (error) {
    console.error('Error saving texts:', error);
  }
};

// Save current text to array
const saveCurrentText = () => {
  const currentText = renderOptions.textValue?.trim();
  if (!currentText) return;

  // Check if text already exists
  if (!savedTexts.value.includes(currentText)) {
    savedTexts.value.push(currentText);
    saveSavedTexts();
  }
};

// Select saved text
const onSelectSavedText = (index) => {
  if (index !== null && index !== undefined && savedTexts.value[index]) {
    renderOptions.textValue = savedTexts.value[index];
  }
};

// Remove saved text
const removeSavedText = (index) => {
  if (index >= 0 && index < savedTexts.value.length) {
    savedTexts.value.splice(index, 1);
    saveSavedTexts();

    // Reset selection if the removed item was selected
    if (selectedSavedText.value === index) {
      selectedSavedText.value = null;
    } else if (selectedSavedText.value > index) {
      selectedSavedText.value--;
    }
  }
};

const loadFontForPreview = (font) => {
  if (!font || font.isSystem || loadedFonts.value.has(font.path)) return;

  const fontName = font.name;
  const fontUrl = `http://localhost:8082/fonts/${font.path}`;
  const fontFace = new FontFace(fontName, `url('${fontUrl}')`);

  fontFace.load().then(() => {
    document.fonts.add(fontFace);
    loadedFonts.value.add(font.path);
  }).catch(error => {
    console.error(`Error loading font ${font.path}:`, error);
  });
};

// Load fonts on component mount
onMounted(async () => {
  await fontService.loadFonts();

  // Preload all fonts for preview
  fontService.getFonts().forEach(font => {
    loadFontForPreview(font);
  });

  // Load saved texts
  loadSavedTexts();
});
</script>

<style scoped>
.flex {
  display: flex;
}

.gap-2 {
  gap: 8px;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

/* Custom styles for the delete button in dropdown */
:deep(.ant-select-item-option-content) {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
}

:deep(.ant-btn-text) {
  padding: 0 !important;
  min-width: 20px !important;
  height: 20px !important;
  line-height: 18px !important;
  font-size: 14px !important;
  font-weight: bold !important;
}
</style>
