<template>
    <a-form-item label="Subtitles Settings" class="border border-gray-600 p-2 rounded-lg">
        <a-checkbox v-model:checked="renderOptions.showSubtitle">
            Show subtitles
        </a-checkbox>
        <div v-if="renderOptions.showSubtitle" class="mt-2">
            <!-- Subtitle Preview -->
            <div class="subtitle-preview h-12 mb-1">
                <p class="subtitle-preview-content" :style="{
                    fontSize: `${renderOptions.subtitleFontSize / 2}px`,
                    fontFamily: renderOptions.fontFamily,
                    color: renderOptions.subtitleTextColor,
                    backgroundColor: renderOptions.subtitleBackgroundColor === 'transparent' ? 'rgba(0,0,0,0.0)' : renderOptions.subtitleBackgroundColor,
                    textAlign: 'center',
                    fontWeight: renderOptions.subtitleBold ? 'bold' : 'normal',
                    _border: `2px solid ${renderOptions.subtitleBorderColor}`,
                    padding: '2px',
                    textShadow:
                        renderOptions.subtitleBorderColor === 'transparent'
                            ? 'none'
                            : `
                -1px -1px 0 ${renderOptions.subtitleBorderColor},
                1px -1px 0 ${renderOptions.subtitleBorderColor},
                -1px  1px 0 ${renderOptions.subtitleBorderColor},
                1px  1px 0 ${renderOptions.subtitleBorderColor},
                -2px  0px 0 ${renderOptions.subtitleBorderColor},
                2px  0px 0 ${renderOptions.subtitleBorderColor},
                0px -2px 0 ${renderOptions.subtitleBorderColor},
                0px  2px 0 ${renderOptions.subtitleBorderColor}
                `,
                }">
                    {{ ttsStore.currentSrtList?.items[0]?.translatedText || ttsStore.currentSrtList?.items[0]?.text
                        || 'Sample Subtitle Text' }}
                </p>
            </div>
            <!-- Color Presets -->
            <div class="color-presets-section mb-4">
                <h4 class="mb-2">Color Presets:</h4>
                <div class="color-presets-grid">
                    <div v-for="preset in subtitleColorPresets" :key="preset.name" class="color-preset-item"
                        @click="applyColorPreset(preset)" :class="{
                            'active': renderOptions.subtitleTextColor === preset.textColor &&
                                renderOptions.subtitleBackgroundColor === preset.backgroundColor
                        }">
                        <div class="color-preview" :style="{
                            color: preset.textColor,
                            backgroundColor: preset.backgroundColor === 'transparent' ? 'rgba(0,0,0,0.1)' : preset.backgroundColor,
                            textShadow: preset.borderColor === 'transparent'
                                ? 'none'
                                : `
                        -1px -1px 0 ${preset.borderColor},
                        1px -1px 0 ${preset.borderColor},
                        -1px  1px 0 ${preset.borderColor},
                        1px  1px 0 ${preset.borderColor},
                        -2px  0px 0 ${preset.borderColor},
                        2px  0px 0 ${preset.borderColor},
                        0px -2px 0 ${preset.borderColor},
                        0px  2px 0 ${preset.borderColor}
                    `,

                            fontWeight: 'bold',
                            fontSize: '18px',
                            padding: '4px 8px',
                            borderRadius: '4px',
                            textAlign: 'center',
                            cursor: 'pointer',
                            minHeight: '24px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                        }">
                            {{ preset.name === 'None' ? '⊘' : 'Text' }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Font Selection -->
            <a-row :gutter="16" class="mb-4">
                <a-col :span="24">
                    <a-form-item label="Font Family">
                        <a-select
                            v-model:value="renderOptions.fontFamily"
                            placeholder="Select font"
                            :loading="!fontService.isReady()"
                        >
                            <a-select-option
                                v-for="font in fontService.getFonts()"
                                :key="font.name"
                                :value="font.name"
                            >
                                <span :style="{ fontFamily: font.isSystem ? font.name : (loadedFonts.has(font.path) ? font.name : 'inherit') }">
                                    {{ font.name }}
                                    <span v-if="!font.isSystem" class="text-gray-400 text-xs ml-2">(Custom)</span>
                                </span>
                            </a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row :gutter="16">
                <a-col :span="5">
                    <a-form-item label="Font Size">
                        <a-input-number v-model:value="renderOptions.subtitleFontSize" :min="12" :max="72"
                            addon-after="px" />
                    </a-form-item>
                </a-col>
                <a-col :span="5">
                    <a-form-item label="Text Color">
                        <a-input v-model:value="renderOptions.subtitleTextColor" type="color" />
                    </a-form-item>
                </a-col>
                <a-col :span="4">
                    <a-form-item label="Background">
                        <a-input v-model:value="renderOptions.subtitleBackgroundColor" type="color" />
                    </a-form-item>
                </a-col>
                <a-col :span="4">
                    <a-form-item label="Border Color">
                        <a-input v-model:value="renderOptions.subtitleBorderColor" type="color" />
                    </a-form-item>
                </a-col>
                <!-- bold -->
                <a-col :span="3">
                    <a-form-item label="Bold">
                        <a-checkbox v-model:checked="renderOptions.subtitleBold">
                            Bold
                        </a-checkbox>
                    </a-form-item>
                </a-col>
            </a-row>
            <!-- assOptions subtitle in video -->
            <!-- left/right slider -->
            <a-row :gutter="16">
                <a-col :span="11">
                    <a-form-item label="Left/Right">
                        <a-slider v-model:value="renderOptions.assOptions.posX" :min="-100" :max="100"
                            :marks="{ '-100': 'Left', '0': 'Center', '100': 'Right' }" :step="1" />
                    </a-form-item>
                </a-col>
                <a-col :span="11">
                    <a-form-item label="Up/Down">
                        <a-slider v-model:value="renderOptions.assOptions.posY" :min="-100" :max="100"
                            :marks="{ '-100': 'Top', '0': 'Center', '100': 'Bottom' }" :step="1" />
                    </a-form-item>
                </a-col>
            </a-row>

            <!-- Additional ASS Options -->
            <a-row :gutter="16" class="mt-2">
                <!-- <a-col :span="8">
            <a-form-item label="Rotation">
                <a-slider
                v-model:value="renderOptions.assOptions.rotation"
                :min="-360"
                :max="360"
                :marks="{ '-360': '-360°', '0': '0°', '360': '360°' }"
                :step="1"
                />
            </a-form-item>
            </a-col> -->
                <a-col :span="8">
                    <a-form-item label="Alignment">
                        <a-select v-model:value="renderOptions.assOptions.align">
                            <a-select-option :value="7">Top Left</a-select-option>
                            <a-select-option :value="8">Top Center</a-select-option>
                            <a-select-option :value="9">Top Right</a-select-option>
                            <a-select-option :value="4">Middle Left</a-select-option>
                            <a-select-option :value="5">Middle Center</a-select-option>
                            <a-select-option :value="6">Middle Right</a-select-option>
                            <a-select-option :value="1">Bottom Left</a-select-option>
                            <a-select-option :value="2">Bottom Center</a-select-option>
                            <a-select-option :value="3">Bottom Right</a-select-option>

                        </a-select>
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="Font Size Override">
                        <a-input-number v-model:value="renderOptions.assOptions.fontSize" :min="12" :max="200"
                            addon-after="px" placeholder="Auto" />
                    </a-form-item>
                </a-col>
            </a-row>

            <!-- ASS Colors Debug -->
            <!-- <a-row :gutter="16" class="mt-2">
            <a-col :span="24">
            <a-button
                size="small"
                type="dashed"
                @click="() => { console.log('Current ASS Colors:', getCurrentASSColors()); message.info('ASS colors logged to console'); }"
            >
                Show ASS Colors (Console)
            </a-button>
            </a-col>
        </a-row> -->
        </div>
    </a-form-item>
</template>
<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { useSubtitleStore } from '@/stores/subtitle-store';
import { useTTSStore } from '@/stores/ttsStore';
import fontService from '@/services/fontService';

const subtitleStore = useSubtitleStore();
const ttsStore = useTTSStore();
const renderOptions = subtitleStore.renderOptions;

// Font loading state
const loadedFonts = ref(new Set());

const loadFontForPreview = (font) => {
  if (!font || font.isSystem || loadedFonts.value.has(font.path)) return;

  const fontName = font.name;
  const fontUrl = `http://localhost:8082/fonts/${font.path}`;
  const fontFace = new FontFace(fontName, `url('${fontUrl}')`);

  fontFace.load().then(() => {
    document.fonts.add(fontFace);
    loadedFonts.value.add(font.path);
  }).catch(error => {
    console.error(`Error loading font ${font.path}:`, error);
  });
};

// Load fonts on component mount
onMounted(async () => {
    await fontService.loadFonts();

    // Preload all fonts for preview
    fontService.getFonts().forEach(font => {
      loadFontForPreview(font);
    });
});

// Subtitle color presets
const subtitleColorPresets = [
    // { name: 'None', textColor: 'transparent', backgroundColor: 'transparent', borderColor: 'transparent' },
    { name: 'White', textColor: '#FFFFFF', backgroundColor: '#000000', borderColor: '#000000' },
    { name: 'Black', textColor: '#000000', backgroundColor: '#FFFFFF', borderColor: '#FFFFFF' },
    { name: 'Blue', textColor: '#FFFFFF', backgroundColor: '#0066CC', borderColor: 'transparent' },
    { name: 'Purple', textColor: '#FFFFFF', backgroundColor: '#6600CC', borderColor: 'transparent' },
    { name: 'Yellow', textColor: '#000000', backgroundColor: '#FFCC00', borderColor: 'transparent' },
    { name: 'Blue Glow', textColor: '#00CCFF', backgroundColor: 'transparent', borderColor: '#0066CC' },
    { name: 'White Glow', textColor: '#FFFFFF', backgroundColor: 'transparent', borderColor: '#000000' },
    { name: 'Green', textColor: '#00FF00', backgroundColor: 'transparent', borderColor: '#006600' },
    { name: 'Pink', textColor: '#FF66CC', backgroundColor: 'transparent', borderColor: '#CC3399' },
    { name: 'Rainbow', textColor: '#FF6600', backgroundColor: 'transparent', borderColor: '#CC3300' },
    { name: 'Gold', textColor: '#FFD700', backgroundColor: 'transparent', borderColor: '#B8860B' }
];



// Convert CSS color to ASS color format
function cssToASSColor(cssColor, opacity = '00') {
    // Remove # if present
    const hex = cssColor.replace('#', '');

    if (hex === 'transparent' || hex === '') {
        return '&HFF000000';
    }

    // Convert hex to RGB
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    // ASS format: &H + opacity + BB + GG + RR (BGR format)
    const assColor = `&H${opacity}${b.toString(16).padStart(2, '0').toUpperCase()}${g.toString(16).padStart(2, '0').toUpperCase()}${r.toString(16).padStart(2, '0').toUpperCase()}`;

    return assColor;
}

// Apply color preset to subtitle options
function applyColorPreset(preset) {
    renderOptions.subtitleTextColor = preset.textColor;
    renderOptions.subtitleBackgroundColor = preset.backgroundColor;
    renderOptions.subtitleBorderColor = preset.borderColor;

    console.log('Applied color preset:', preset.name);
    console.log('ASS Colors:', {
        text: cssToASSColor(preset.textColor),
        background: cssToASSColor(preset.backgroundColor, '90'),
        border: cssToASSColor(preset.borderColor)
    });

    message.success(`Applied ${preset.name} color preset`);
}

// Get current ASS colors for debugging
function getCurrentASSColors() {
    return {
        text: cssToASSColor(renderOptions.subtitleTextColor),
        background: cssToASSColor(renderOptions.subtitleBackgroundColor, '90'),
        border: cssToASSColor(renderOptions.subtitleBorderColor)
    };
}



</script>
<style scoped>
/* Color Presets Styles */
.color-presets-section {
  margin-bottom: 16px;
}

.color-presets-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 8px;
  margin-bottom: 16px;
}

.color-preset-item {
  position: relative;
}

.color-preset-item.active .color-preview {
  box-shadow: 0 0 0 3px #1890ff;
  transform: scale(1.05);
}

.color-preview {
  transition: all 0.2s ease;
  user-select: none;
}

.color-preview:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
.subtitle-preview {
    border-radius: 4px;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 10px 10px;
    background-position: 0 0, 0 5px, 5px -5px, -5px 0px;
}

.subtitle-preview-content {
  margin: 0;
  max-width: 100%;
  word-wrap: break-word;
}

</style>